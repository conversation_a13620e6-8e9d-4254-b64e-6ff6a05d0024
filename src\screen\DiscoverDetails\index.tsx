import { useEffect, useState } from 'react';
import { BreadcrumbItem, Breadcrumbs, Chip } from '@heroui/react';
import { SlLocationPin } from 'react-icons/sl';

import PrimaryFilterSkeleton from '@/components/loaders/PrimaryFilterSkeleton';
import PrimaryFilter from '@/components/globalComponents/primaryFilter';
import ChatSkeleton from '@/components/loaders/ChatSkeleton';
import { AnimatedIcon } from '@/components/icons';

import ChatWithShasa from '../landing/ChatWithShasa';
import { IoHeartOutline } from "react-icons/io5";
import { FiDownload } from "react-icons/fi";
import { GrShareOption } from "react-icons/gr";

interface DiscoverPageProps {
  isLoading?: boolean;
  loadingComponents?: {
    filter?: boolean;
    chat?: boolean;
    recommendations?: boolean;
  };
}
const DiscoverDetailsPage = ({
  isLoading = false,
  loadingComponents = {},
}: DiscoverPageProps) => {
  const [componentLoading, setComponentLoading] = useState({
    filter: loadingComponents.filter || isLoading,
    chat: loadingComponents.chat || isLoading,
    recommendations: loadingComponents.recommendations || isLoading,
  });
  // Simulate staggered loading for better UX
  useEffect(() => {
    if (isLoading) {
      // Filter loads first
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, filter: false }));
      }, 800);

      // Chat loads second
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, chat: false }));
      }, 1200);

      // Recommendations load last
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, recommendations: false }));
      }, 1600);
    }
  }, [isLoading]);
  return (
    <div className="p-3 sm:p-5 rounded-tl-xl h-[calc(100vh-73px)] flex flex-col">
      <div className="">
        <Breadcrumbs size="lg">
          <BreadcrumbItem>Discover</BreadcrumbItem>
          <BreadcrumbItem>Discover</BreadcrumbItem>
          <BreadcrumbItem>Historic Germany</BreadcrumbItem>
        </Breadcrumbs>
        <div className="flex flex-row items-center justify-between">
          <div className="text-2xl sm:text-4xl font-semibold">
            Historic Germany
          </div>
          <div className="flex flex-row items-center gap-3">
            <button
              type="button"
              className="px-5 py-2 rounded-full btn-gradient text-white text-sm font-semibold shadow-md hover:opacity-90 transition"
            >
              Book this Plan
            </button>
            <Chip
              color="primary"
              variant="flat"
              size="md"
              className="font-semibold h-[40px]"
            >
              <AnimatedIcon size={20} />
            </Chip>
            <Chip
              color="primary"
              variant="flat"
              size="md"
              className="font-semibold h-[40px]"
            >
              <FiDownload size={20} />
            </Chip>
            <Chip
              color="primary"
              variant="flat"
              size="md"
              className="font-semibold h-[40px]"
            >
              <GrShareOption size={20} />
            </Chip>
          </div>
        </div>
      </div>

      {/* Primary Filter Section */}
      <div className="py-3 sm:py-4">
        {componentLoading.filter ? (
          <PrimaryFilterSkeleton />
        ) : (
          <PrimaryFilter />
        )}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-6 gap-3 sm:gap-4 flex-1 min-h-0">
        {/* Chat Section */}
        <div className="lg:col-span-2 order-2 lg:order-1">
          {componentLoading.chat ? <ChatSkeleton /> : <ChatWithShasa />}
        </div>

        {/* Recommendations Section */}
        <div className="lg:col-span-4 order-1 lg:order-2">
          {/* {componentLoading.recommendations ? (
            <RecommendationSkeleton cardCount={3} />
          ) : (
            <Recommendation />
          )} */}
        </div>
      </div>
    </div>
  );
};

export default DiscoverDetailsPage;

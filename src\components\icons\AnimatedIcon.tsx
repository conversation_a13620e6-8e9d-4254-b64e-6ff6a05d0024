import React from 'react';

interface AnimatedIconProps {
  size?: number;
  className?: string;
  color?: string;
}

const AnimatedIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={`animated-icon ${className}`}
      xmlns="http://www.w3.org/2000/svg"
    >
      <style>
        {`
          .animated-icon {
            animation: pulse 2s ease-in-out infinite;
          }
          
          .icon-path {
            animation: draw 3s ease-in-out infinite;
            stroke-dasharray: 200;
            stroke-dashoffset: 200;
          }
          
          .icon-circle {
            animation: rotate 4s linear infinite;
            transform-origin: 50px 50px;
          }
          
          .icon-heart {
            animation: heartbeat 1.5s ease-in-out infinite;
            transform-origin: 50px 50px;
          }
          
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
          
          @keyframes draw {
            0% { stroke-dashoffset: 200; }
            50% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -200; }
          }
          
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          
          @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            25% { transform: scale(1.1); }
            50% { transform: scale(1); }
            75% { transform: scale(1.05); }
          }
        `}
      </style>

      {/* Animated Travel/Plane Icon */}
      <g className="icon-circle">
        <circle
          cx="50"
          cy="50"
          r="40"
          fill="none"
          stroke={color}
          strokeWidth="2"
          strokeDasharray="5,5"
        />
      </g>

      <g className="icon-heart">
        <path
          d="M50 75 C30 55, 10 35, 30 25 C40 20, 50 30, 50 30 C50 30, 60 20, 70 25 C90 35, 70 55, 50 75 Z"
          fill={color}
          opacity="0.8"
        />
      </g>

      <path
        className="icon-path"
        d="M20 50 Q35 30, 50 50 T80 50"
        fill="none"
        stroke={color}
        strokeWidth="3"
        strokeLinecap="round"
      />

      {/* Animated dots */}
      <circle cx="25" cy="45" r="2" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="2s"
          repeatCount="indefinite"
          begin="0s"
        />
      </circle>
      <circle cx="50" cy="35" r="2" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="2s"
          repeatCount="indefinite"
          begin="0.5s"
        />
      </circle>
      <circle cx="75" cy="45" r="2" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="2s"
          repeatCount="indefinite"
          begin="1s"
        />
      </circle>
    </svg>
  );
};

// Animated Plane Icon Component
const AnimatedPlaneIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={`animated-plane-icon ${className}`}
      xmlns="http://www.w3.org/2000/svg"
    >
      <style>
        {`
          .animated-plane-icon {
            animation: float 3s ease-in-out infinite;
          }

          .plane-body {
            animation: fly 4s linear infinite;
            transform-origin: 50px 50px;
          }

          .plane-trail {
            animation: trail 2s ease-in-out infinite;
            stroke-dasharray: 10;
            stroke-dashoffset: 20;
          }

          .cloud {
            animation: drift 6s ease-in-out infinite;
          }

          .propeller {
            animation: spin 0.1s linear infinite;
            transform-origin: 35px 45px;
          }

          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
          }

          @keyframes fly {
            0% { transform: translateX(-10px); }
            50% { transform: translateX(5px); }
            100% { transform: translateX(-10px); }
          }

          @keyframes trail {
            0% { stroke-dashoffset: 20; opacity: 1; }
            100% { stroke-dashoffset: -20; opacity: 0.3; }
          }

          @keyframes drift {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(10px); }
          }

          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>

      {/* Clouds */}
      <g className="cloud">
        <ellipse cx="20" cy="25" rx="8" ry="4" fill={color} opacity="0.3" />
        <ellipse cx="75" cy="20" rx="6" ry="3" fill={color} opacity="0.2" />
      </g>

      {/* Plane body */}
      <g className="plane-body">
        {/* Main fuselage */}
        <ellipse cx="50" cy="45" rx="25" ry="4" fill={color} />

        {/* Wings */}
        <ellipse cx="45" cy="45" rx="15" ry="2" fill={color} />

        {/* Tail */}
        <polygon points="70,45 75,40 75,50" fill={color} />

        {/* Cockpit */}
        <circle cx="30" cy="45" r="3" fill={color} opacity="0.8" />
      </g>

      {/* Propeller */}
      <g className="propeller">
        <line x1="30" y1="45" x2="40" y2="45" stroke={color} strokeWidth="1" />
        <line x1="35" y1="40" x2="35" y2="50" stroke={color} strokeWidth="1" />
      </g>

      {/* Flight trail */}
      <path
        className="plane-trail"
        d="M75 45 Q85 40, 95 45 Q85 50, 75 45"
        fill="none"
        stroke={color}
        strokeWidth="2"
        opacity="0.6"
      />

      {/* Animated stars/sparkles */}
      <circle cx="15" cy="60" r="1" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0s"
        />
      </circle>
      <circle cx="85" cy="65" r="1" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0.7s"
        />
      </circle>
      <circle cx="60" cy="75" r="1" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="1.2s"
        />
      </circle>
    </svg>
  );
};

export default AnimatedIcon;
export { AnimatedPlaneIcon };

// Animated Plane Icon Component
const HeartIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width="19"
      height="17"
      viewBox="0 0 19 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.62444 14.7599L7.08876 14.1709L6.62444 14.7599ZM9.15633 3.58471L8.61597 4.10482C8.75735 4.25171 8.95245 4.33471 9.15633 4.33471C9.36021 4.33471 9.55531 4.25171 9.6967 4.10482L9.15633 3.58471ZM11.6882 14.7599L12.1525 15.3489L11.6882 14.7599ZM6.62444 14.7599L7.08876 14.1709C5.81829 13.1694 4.45104 12.2072 3.36503 10.9843C2.30474 9.7904 1.573 8.40726 1.573 6.6151H0.822998H0.072998C0.072998 8.87539 1.01602 10.5982 2.24347 11.9804C3.4452 13.3336 4.97867 14.4175 6.16013 15.3489L6.62444 14.7599ZM0.822998 6.6151H1.573C1.573 4.86948 2.55919 3.41209 3.89526 2.80137C5.18405 2.21226 6.93298 2.35626 8.61597 4.10482L9.15633 3.58471L9.6967 3.06461C7.62982 0.917201 5.21208 0.550169 3.27167 1.43714C1.37854 2.30249 0.072998 4.30828 0.072998 6.6151H0.822998ZM6.62444 14.7599L6.16013 15.3489C6.58584 15.6845 7.0508 16.0489 7.52395 16.3254C7.99686 16.6017 8.54927 16.8342 9.15633 16.8342V16.0842V15.3342C8.93006 15.3342 8.64914 15.2456 8.28074 15.0303C7.91258 14.8151 7.52826 14.5174 7.08876 14.1709L6.62444 14.7599ZM11.6882 14.7599L12.1525 15.3489C13.334 14.4175 14.8675 13.3336 16.0692 11.9804C17.2966 10.5982 18.2397 8.87539 18.2397 6.6151H17.4897H16.7397C16.7397 8.40726 16.0079 9.7904 14.9476 10.9843C13.8616 12.2072 12.4944 13.1694 11.2239 14.1709L11.6882 14.7599ZM17.4897 6.6151H18.2397C18.2397 4.30828 16.9341 2.30249 15.041 1.43714C13.1006 0.550169 10.6828 0.917201 8.61597 3.06461L9.15633 3.58471L9.6967 4.10482C11.3797 2.35626 13.1286 2.21226 14.4174 2.80137C15.7535 3.41209 16.7397 4.86948 16.7397 6.6151H17.4897ZM11.6882 14.7599L11.2239 14.1709C10.7844 14.5174 10.4001 14.8151 10.0319 15.0303C9.66352 15.2456 9.3826 15.3342 9.15633 15.3342V16.0842V16.8342C9.7634 16.8342 10.3158 16.6017 10.7887 16.3254C11.2619 16.0489 11.7268 15.6845 12.1525 15.3489L11.6882 14.7599Z"
        fill="#707FF5"
      />
    </svg>
  );
};

export { HeartIcon };

import React from 'react';

interface AnimatedIconProps {
  size?: number;
  className?: string;
  color?: string;
}

const AnimatedIcon: React.FC<AnimatedIconProps> = ({ 
  size = 24, 
  className = '', 
  color = 'currentColor' 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={`animated-icon ${className}`}
      xmlns="http://www.w3.org/2000/svg"
    >
      <style>
        {`
          .animated-icon {
            animation: pulse 2s ease-in-out infinite;
          }
          
          .icon-path {
            animation: draw 3s ease-in-out infinite;
            stroke-dasharray: 200;
            stroke-dashoffset: 200;
          }
          
          .icon-circle {
            animation: rotate 4s linear infinite;
            transform-origin: 50px 50px;
          }
          
          .icon-heart {
            animation: heartbeat 1.5s ease-in-out infinite;
            transform-origin: 50px 50px;
          }
          
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
          
          @keyframes draw {
            0% { stroke-dashoffset: 200; }
            50% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -200; }
          }
          
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          
          @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            25% { transform: scale(1.1); }
            50% { transform: scale(1); }
            75% { transform: scale(1.05); }
          }
        `}
      </style>
      
      {/* Animated Travel/Plane Icon */}
      <g className="icon-circle">
        <circle
          cx="50"
          cy="50"
          r="40"
          fill="none"
          stroke={color}
          strokeWidth="2"
          strokeDasharray="5,5"
        />
      </g>
      
      <g className="icon-heart">
        <path
          d="M50 75 C30 55, 10 35, 30 25 C40 20, 50 30, 50 30 C50 30, 60 20, 70 25 C90 35, 70 55, 50 75 Z"
          fill={color}
          opacity="0.8"
        />
      </g>
      
      <path
        className="icon-path"
        d="M20 50 Q35 30, 50 50 T80 50"
        fill="none"
        stroke={color}
        strokeWidth="3"
        strokeLinecap="round"
      />
      
      {/* Animated dots */}
      <circle cx="25" cy="45" r="2" fill={color}>
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="0s"/>
      </circle>
      <circle cx="50" cy="35" r="2" fill={color}>
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="0.5s"/>
      </circle>
      <circle cx="75" cy="45" r="2" fill={color}>
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="1s"/>
      </circle>
    </svg>
  );
};

export default AnimatedIcon;
